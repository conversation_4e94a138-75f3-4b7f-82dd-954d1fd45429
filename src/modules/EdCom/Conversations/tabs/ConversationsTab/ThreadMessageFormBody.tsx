import React, { useMemo, useRef, useCallback, forwardRef } from 'react';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import AttachmentField from '../../../../../common/components/containers/EntityForm/fields/AttachmentField';
import { ED_COM_CONVERSATION_ATTACHMENTS } from '../../../../../fsCategories';
import EntityFormFieldSet from '../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import QuoteMessageField from './QuoteMessageField';
import useStorageSettings from '../../../../../common/components/controls/base/TextEditor/useStorageSettings';
import useCurrentUser from '../../../../../common/data/hooks/useCurrentUser';
import { ICustomButton } from '../../../../../common/components/controls/base/TextEditor/useCustomButtons';
import { TextEditorRef } from '../../../../../common/components/controls/base/TextEditor/TextEditor';
import styles from './ThreadMessageFormBody.scss';
import TextEditorField from '../../../../../common/components/containers/EntityForm/fields/TextEditorField';
import useEntityFormContext from '../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';

interface IThreadMessageFromBody {
  className?: string;
  isEditMode?: boolean;
  setIsEditMode?: React.Dispatch<React.SetStateAction<boolean>>;
  editingMessageId?: number | null;
  setEditingMessageId?: React.Dispatch<React.SetStateAction<number | null>>;
  parentId?: number | null;
  setIsTextBoxOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}

const ThreadMessageFromBody = forwardRef<TextEditorRef, IThreadMessageFromBody>(
  (
    {
      className,
      isEditMode,
      setIsEditMode,
      editingMessageId,
      setEditingMessageId,
      parentId,
      setIsTextBoxOpen,
    },
    ref,
  ) => {
    const {
      submitForm,
      touched,
      isSubmitting,
      dirty,
      setValues,
      values,
    } = useEntityFormContext();
    const {
      me: { organisationGroupId, tenantId },
    } = useCurrentUser();

    const { maxSize } = useStorageSettings(
      ED_COM_CONVERSATION_ATTACHMENTS,
      tenantId,
      organisationGroupId,
    );

    const hiddenAttachButtonRef = useRef<HTMLButtonElement>(null);

    const HiddenAttachmentComponent = useCallback(
      ({ onModalOpen }: { onModalOpen: () => void }) => (
        <button
          ref={hiddenAttachButtonRef}
          className="hidden"
          type="button"
          onClick={onModalOpen}
        />
      ),
      [],
    );

    const currentMessageId = values?.id;

    // Handle keyboard events for Enter/Shift+Enter behavior
    const handleKeyDown = useCallback(
      function (this: any, event: any) {
        console.log
        const ENTER_KEY = 13;

        if (
          (event.keyCode === ENTER_KEY || event.which === ENTER_KEY) &&
          !event.shiftKey &&
          !event.ctrlKey &&
          !event.altKey
        ) {
          event.preventDefault();

          const content = values?.content || '';
          const attachments = values?.attachments || [];

          if (!isEmpty(content.trim()) || !isEmpty(attachments)) {
            if (!isEmpty(touched) && !isSubmitting && dirty) {
              submitForm();
            }
          }

          return false;
        }

        return true;
      },
      [values, touched, isSubmitting, dirty, submitForm],
    );

    const customButtons: ICustomButton[] = useMemo(() => {
      const actions = [
        {
          id: 'fileAttachment',
          title: 'Attach File',
          icon: 'file-plus',
          onClick: () => {
            if (hiddenAttachButtonRef.current) {
              hiddenAttachButtonRef.current.click();
            }
          },
        },
      ];

      const isThisEditorInEditMode = parentId
        ? editingMessageId === currentMessageId
        : isEditMode;

      if (!isThisEditorInEditMode) {
        actions.push({
          id: 'send',
          title: 'Send',
          icon: 'arrow-right14',
          onClick: () => {
            if (!isEmpty(touched) && !isSubmitting && dirty) {
              submitForm();
            }
          },
        });
      }

      if (isThisEditorInEditMode) {
        actions.push({
          id: 'cancel',
          title: 'Cancel',
          icon: 'cross2',
          onClick: () => {
            setValues({ content: '' });
            if (parentId) {
              setEditingMessageId && setEditingMessageId(null);
              setIsTextBoxOpen && setIsTextBoxOpen(false);
            } else {
              setIsEditMode && setIsEditMode(false);
            }
          },
        });
        actions.push({
          id: 'edit',
          title: 'Edit',
          icon: 'checkmark3',
          onClick: () => {
            if (!isEmpty(touched) && !isSubmitting && dirty) {
              submitForm();
              if (parentId) {
                setEditingMessageId && setEditingMessageId(null);
                setIsTextBoxOpen && setIsTextBoxOpen(false);
              } else {
                setIsEditMode && setIsEditMode(false);
              }
            }
          },
        });
      }

      return actions;
    }, [
      isSubmitting,
      touched,
      dirty,
      submitForm,
      isEditMode,
      setIsEditMode,
      setValues,
      parentId,
      editingMessageId,
      setEditingMessageId,
      currentMessageId,
      setIsTextBoxOpen,
    ]);

    return (
      <EntityFormFieldSet className={className}>
        <QuoteMessageField />
        <div className={styles.showScrollbars}>
          <TextEditorField
            ref={ref}
            noLabel
            columns={1}
            config={{
              events: {
                keydown: handleKeyDown,
              },
            }}
            customButtons={customButtons}
            name="content"
            rows={4}
          />
        </div>
        <div
          className={classNames('pl-10 pr-10', {
            hidden: isSubmitting,
          })}
        >
          <AttachmentField
            isDownloadable
            isThreadAttachment
            showAttachmentNameOnly
            addAttachmentComponent={HiddenAttachmentComponent}
            categoryKey={ED_COM_CONVERSATION_ATTACHMENTS}
            columns={1}
            hasLowStrike={false}
            isListHidden={false}
            maxSize={maxSize}
            name="attachments"
            wrapperClassNames={{
              1: classNames('col-lg-12', styles.fieldWrapperClass),
            }}
          />
        </div>
      </EntityFormFieldSet>
    );
  },
);

ThreadMessageFromBody.displayName = 'ThreadMessageFromBody';

export default ThreadMessageFromBody;
