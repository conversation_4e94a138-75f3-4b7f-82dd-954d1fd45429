/* global $ */
/*global swal*/
import classNames from 'classnames';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import DropZone from 'react-dropzone';
import { withRouter } from 'react-router-dom';
import uuidv4 from 'uuid/v4';
import { round } from 'lodash';
import imageCompression from 'browser-image-compression';

import Notifications from '../../../../common/utils/Notifications';
import { PERSON_PHOTO } from '../../../../fsCategories';

import withFsClient from '../../../data/upload/withFsClient';
import { toKb } from '../../../fileSizeHelper';
import translatePropTypes from '../../../translatePropTypes';
import Cropper from '../../utils/Cropper';

import Ripple from '../../utils/Ripple';
import Spinner from '../../utils/Spinner';
import withTranslations from '../../utils/Translations/withTranslations';
import WebCam from '../../utils/WebCam';
import styles from './styles.scss';
import swal2, { Swal } from '../../../utils/swal2';

const ROUND_TO_ONE = 1;
const ROUND_TO = 3;
const BYTES_IN_MEGABYTE = 1048576;
const BYTES_IN_KILOBYTE = 1024;

@withRouter
@withTranslations
@withFsClient
export default class UploadPhotoModalContent extends Component {
  static propTypes = {
    onUpdateProfilePhoto: PropTypes.func.isRequired,
    fileId: PropTypes.string,
    fileCategory: PropTypes.string,
    hasSubtitle: PropTypes.bool,
    subtitle: PropTypes.string,
    hasWebcam: PropTypes.bool,
    hasDescription: PropTypes.bool,
    maxSize: PropTypes.number,
    ...withFsClient.props,
    ...translatePropTypes,
  };

  static defaultProps = {
    fileId: null,
    fileCategory: PERSON_PHOTO,
    hasSubtitle: true,
    subtitle: undefined,
    hasWebcam: true,
    hasDescription: true,
  };

  state = {
    avatarUrl: null,
    playVideo: false,
    takePhoto: false,
    videoError: null,
    uploading: false,
    mimeType: 'image/png',
    isCropping: false,
  };

  handleResizeAndUpload = async (file, fileName) => {
    const { maxSize } = this.props;

    if (!maxSize) return;
    const compressFile = async inputFile => {
      const compressedFile = await imageCompression(inputFile, {
        maxSizeMB: maxSize / BYTES_IN_MEGABYTE,
        useWebWorker: true,
      });
      if (compressedFile.size > maxSize) {
        return await compressFile(compressedFile);
      }
      return compressedFile;
    };

    const finalCompressedFile = await compressFile(file);

    return new File([finalCompressedFile], fileName, {
      type: 'image/png',
    });
  };

  checkFileSize = async (file, fileName) => {
    const { maxSize, t } = this.props;
    if (maxSize && file.size > maxSize) {
      const result = await swal2({
        text: t(
          `Photo exceeds the maximum allowed size of ${
            round(maxSize / BYTES_IN_MEGABYTE, ROUND_TO) >= 1
              ? `${round(maxSize / BYTES_IN_MEGABYTE, ROUND_TO_ONE)} MB`
              : `${round(maxSize / BYTES_IN_KILOBYTE)} KB`
          }`,
        ),
        icon: 'info',
        className: classNames(styles.modalWrapper),
        confirmButtonText: t('Resize'),
        cancelButtonText: t('Cancel'),
        preConfirm: async () => {
          const confirmButton = Swal.getConfirmButton();
          if (confirmButton) {
            confirmButton.innerHTML = '<i class="icon-spinner4 spinner" />';
            confirmButton.disabled = true; // Disable the button while loading
          }

          const result = await this.handleResizeAndUpload(file, fileName);
          return result;
        },
      });
      if (result.isConfirmed && result.value) {
        return result.value;
      }
      return false;
    }
    return true;
  };

  handleDragDrop = results => {
    const { t } = this.props;
    const reader = new FileReader();
    if (results) {
      reader.onloadend = e => {
        const avatarUrl = e.target.result;

        // In case when mimeType doesn't correct - try to use magic numbers
        // https://stackoverflow.com/questions/18299806/how-to-check-file-mime-type-with-javascript-before-upload#answer-29672957

        const mimeType = avatarUrl.substring(
          'data:'.length,
          avatarUrl.indexOf(';base64'),
        );
        this.setState({
          mimeType,
          avatarUrl,
        });
      };
      try {
        reader.readAsDataURL(results[0]);
      } catch {
        swal({
          text: t(`File type is not allowed.`),
          icon: 'warning',
          buttons: [t('Cancel'), t('Ok')],
          dangerMode: true,
        });
      }
    }
  };

  handleWebCamToggle = () => {
    this.setState(state => ({
      videoError: null,
      playVideo: !state.playVideo,
    }));
  };

  handleDropZoneOpen = () => {
    this.setState({
      videoError: null,
      playVideo: false,
    });

    if (this.dropZone) {
      this.dropZone.open();
    }
  };

  handleTakePicture = () => {
    this.setState({ takePhoto: true });
  };

  handleBackClick = () => {
    this.setState({ avatarUrl: null });
    this.handleStateReset();
  };

  handleCameraError = error => {
    this.setState({ videoError: error });
  };

  handleStateReset = () => {
    this.setState({
      playVideo: false,
      takePhoto: false,
      videoError: null,
      uploading: false,
      isCropping: false,
    });
  };

  handleImageCrop = () => {
    const { mimeType } = this.state;
    const qualitySettings = 0.8;
    this.setState({ isCropping: true });

    return new Promise(resolve => {
      $(this.crp.cropper)
        .cropper('getCroppedCanvas')
        .toBlob(resolve, mimeType, qualitySettings);
    });
  };

  handleImageUpload = async () => {
    const {
      onUpdateProfilePhoto,
      fileId,
      update,
      personId,
      t,
      fsClient,
      organisationGroupId,
      tenantId,
      fileCategory,
    } = this.props;
    const { mimeType } = this.state;

    const blob = await this.handleImageCrop();

    const fileName = uuidv4();

    let file = {
      fileName,
      mimeType,
      fileId,
      fileSize: blob.size,
      data: blob,
    };

    const response = await this.checkFileSize(blob, fileName);

    if (response) {
      if (response !== true) {
        const newBlob = new Blob([response], { type: response.type });
        file = {
          ...file,
          fileSize: newBlob.size,
          data: newBlob,
        };
      }
      try {
        this.setState({ uploading: true });

        const config = await fsClient.getUploadUrl(file, fileCategory, {
          organisationGroupId,
          tenantId,
        });

        const { uploadToken } = config;

        file.uploadToken = uploadToken;

        await fsClient.uploadFile(file, config);

        const { fileId, version } = await fsClient.commitUpload(uploadToken);

        onUpdateProfilePhoto({ fileId, version });
        if (!!personId) {
          update({
            variables: {
              id: personId,
              photoFileId: fileId,
            },
          });
        }
      } catch (e) {
        Notifications.error(
          t('Error'),
          t(e.message, {
            maxFileSize: toKb(e.maxFileSize),
            canUploadSize: toKb(e.canUploadSize),
          }),
          t,
        );
      }
    }

    this.setState({ uploading: false });

    this.handleStateReset();
  };

  getTitle(title, isPending) {
    const { t } = this.props;
    return isPending ? <Spinner /> : t(title);
  }

  handleBrowseTakePictureButtonClick = () => {
    const resolvedFunc = this.state.playVideo
      ? this.handleTakePicture
      : this.handleDropZoneOpen;

    this.state.avatarUrl ? this.handleStateReset() : resolvedFunc();
  };

  setUpCropper = canvas => {
    this.setState({
      avatarUrl: canvas.toDataURL(),
    });
  };

  get subtitle() {
    const { hasSubtitle, subtitle, t } = this.props;

    return hasSubtitle
      ? subtitle ||
          t(
            'Your friends will be easier to find you if you upload your real photo',
          )
      : null;
  }

  get description() {
    const { hasDescription, t, hasWebcam } = this.props;

    return hasDescription ? (
      <p>
        {`${t(
          'You can make an instant photo, if your device is equipped with a',
        )} '`}

        {hasWebcam ? (
          <>
            <a onClick={this.handleWebCamToggle}>{t('webcam')}</a>
            {` ${t('or')} `}
          </>
        ) : null}

        <a onClick={this.handleDropZoneOpen}>{t('upload images')}</a>
        {` ${t('in JPG, GIF or PNG')}`}
      </p>
    ) : null;
  }

  render() {
    const {
      videoError,
      avatarUrl,
      takePhoto,
      playVideo,
      uploading,
      isCropping,
    } = this.state;

    const { t, hasWebcam } = this.props;

    const btnContainerStyle = classNames('mt-15', {
      'crop-btns text-right pt-15': avatarUrl,
      'input-group-btn': !avatarUrl,
    });
    const webCamButton = classNames('btn legitRipple', {
      'btn-primary btn-rounded': !this.state.playVideo || this.state.avatarUrl,
    });
    const btnStyles = classNames('btn btn-link legitRipple uploadFile', {
      'mr-5': this.state.avatarUrl,
    });
    return (
      <div className="modal-body text-center">
        <p>{this.subtitle}</p>

        {this.description}

        <div
          className={classNames('panel-upload', {
            [styles.photoPreview]: !!avatarUrl,
          })}
        >
          {!avatarUrl && hasWebcam ? (
            <WebCam
              containerClassName="avatar-container"
              containerElement="div"
              play={playVideo}
              takePhoto={takePhoto}
              takePicture={this.setUpCropper}
              onError={this.handleCameraError}
            />
          ) : null}
          {avatarUrl && (
            <Cropper ref={cropperWrapper => (this.crp = cropperWrapper)}>
              <img alt="" src={avatarUrl} />
            </Cropper>
          )}
          <DropZone
            ref={dropZone => (this.dropZone = dropZone)}
            accept="image/jpeg, image/png"
            acceptClassName="file-highlighted"
            className={classNames('file-drop-zone clickable', {
              hide: uploading || playVideo || avatarUrl,
            })}
            disabled={uploading}
            multiple={false}
            onDrop={this.handleDragDrop}
          >
            <div className="file-drop-zone-title">
              {`${t('Drag & drop files here')} …`}
              <br />( {`${t('or click to select file')}`} )
            </div>
          </DropZone>
        </div>
        <div className={btnContainerStyle}>
          {!avatarUrl && !videoError && (
            <Ripple>
              <button
                className={btnStyles}
                type="button"
                onClick={this.handleBrowseTakePictureButtonClick}
              >
                {this.state.playVideo ? t('Take a picture') : t('Browse file')}
              </button>
            </Ripple>
          )}
          {avatarUrl && (
            <>
              <Ripple>
                <button
                  className={btnStyles}
                  disabled={uploading || isCropping}
                  type="button"
                  onClick={this.handleBackClick}
                >
                  {t('Clear')}
                </button>
              </Ripple>
              <Ripple>
                <button
                  className={webCamButton}
                  disabled={uploading}
                  type="button"
                  onClick={this.handleImageUpload}
                >
                  {this.getTitle('Save and Upload', uploading)}
                </button>
              </Ripple>
            </>
          )}
          {!avatarUrl && hasWebcam ? (
            <Ripple>
              <button
                className={webCamButton}
                disabled={uploading}
                type="button"
                onClick={this.handleWebCamToggle}
              >
                {t(playVideo ? 'Back' : 'Take Webcam Photo')}
              </button>
            </Ripple>
          ) : null}
        </div>
        <div className="input-group file-caption-main" />
      </div>
    );
  }
}
